window.PDFViewerComponent = {
    instances: new Map(),

    init: function (config) {
        const { id, url, toolbar = true } = config;

        console.log("Initializing PDF viewer with ID:", id);

        // 检查PDF.js是否已加载
        if (typeof window["pdfjs-dist/build/pdf"] === "undefined") {
            console.error("PDF.js library not loaded");
            return;
        }

        // PDF.js配置
        const pdfjsLib = window["pdfjs-dist/build/pdf"];
        pdfjsLib.GlobalWorkerOptions.workerSrc = "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js";

        // 组件实例状态
        const instance = {
            id,
            pdfDoc: null,
            currentPage: 1,
            isRendering: false,
            pendingPage: null,
            toolbar,
            resizeObserver: null,
        };

        // DOM元素
        const canvas = document.getElementById(`pdf-canvas-${id}`);
        const context = canvas?.getContext("2d");
        const container = document.getElementById(`pdf-container-${id}`);
        const loading = document.getElementById(`loading-${id}`);

        // 验证基本元素存在
        if (!canvas || !context || !container || !loading) {
            console.error("Required DOM elements not found for PDF viewer:", id);
            return;
        }

        instance.canvas = canvas;
        instance.context = context;
        instance.container = container;
        instance.loading = loading;

        if (toolbar) {
            const prevBtn = document.getElementById(`prev-${id}`);
            const nextBtn = document.getElementById(`next-${id}`);
            const pageNumSpan = document.getElementById(`page-num-${id}`);
            const pageCountSpan = document.getElementById(`page-count-${id}`);

            // 验证工具栏元素存在
            if (!prevBtn || !nextBtn || !pageNumSpan || !pageCountSpan) {
                console.error("Toolbar elements not found for PDF viewer:", id);
                return;
            }

            instance.prevBtn = prevBtn;
            instance.nextBtn = nextBtn;
            instance.pageNumSpan = pageNumSpan;
            instance.pageCountSpan = pageCountSpan;

            // 绑定事件
            this.bindEvents(instance);
        }

        // 设置容器大小变化监听
        this.setupResizeObserver(instance);

        // 存储实例
        this.instances.set(id, instance);

        // 加载PDF
        this.loadPDF(instance, url);
    },

    setupResizeObserver: function (instance) {
        if (typeof ResizeObserver !== "undefined") {
            instance.resizeObserver = new ResizeObserver(() => {
                if (instance.pdfDoc && instance.currentPage) {
                    this.renderPage(instance, instance.currentPage);
                }
            });
            instance.resizeObserver.observe(instance.container);
        }

        // 备用方案：窗口大小变化监听
        window.addEventListener("resize", () => {
            if (instance.pdfDoc && instance.currentPage) {
                setTimeout(() => {
                    this.renderPage(instance, instance.currentPage);
                }, 100);
            }
        });
    },

    renderPage: function (instance, pageNumber) {
        if (instance.isRendering) {
            instance.pendingPage = pageNumber;
            return;
        }

        instance.isRendering = true;

        instance.pdfDoc.getPage(pageNumber).then((page) => {
            // 直接使用容器的实际像素尺寸
            const containerWidth = instance.container.clientWidth;
            const containerHeight = instance.container.clientHeight;

            console.log("Container size:", containerWidth, "x", containerHeight);

            // 设置canvas实际像素尺寸与容器一致
            instance.canvas.width = containerWidth;
            instance.canvas.height = containerHeight;
            instance.canvas.style.width = containerWidth + "px";
            instance.canvas.style.height = containerHeight + "px";
            instance.canvas.style.position = "absolute";
            instance.canvas.style.top = "0";
            instance.canvas.style.left = "0";

            console.log("Canvas after:", instance.canvas.width, "x", instance.canvas.height);

            // 计算缩放比例，强制填满
            const viewport = page.getViewport({ scale: 1 });
            const scale = Math.max(containerWidth / viewport.width, containerHeight / viewport.height);
            const scaledViewport = page.getViewport({ scale });

            // 清除画布
            instance.context.clearRect(0, 0, containerWidth, containerHeight);

            // 计算居中偏移
            const offsetX = (containerWidth - scaledViewport.width) / 2;
            const offsetY = (containerHeight - scaledViewport.height) / 2;

            // 保存上下文状态并应用偏移
            instance.context.save();
            instance.context.translate(offsetX, offsetY);

            const renderContext = {
                canvasContext: instance.context,
                viewport: scaledViewport,
            };

            const renderTask = page.render(renderContext);

            renderTask.promise.then(() => {
                // 恢复上下文状态
                instance.context.restore();

                instance.isRendering = false;
                if (instance.pendingPage !== null) {
                    this.renderPage(instance, instance.pendingPage);
                    instance.pendingPage = null;
                }
            });
        });

        // 更新UI
        instance.currentPage = pageNumber;
        if (instance.toolbar) {
            instance.pageNumSpan.textContent = pageNumber;
            instance.prevBtn.disabled = pageNumber <= 1;
            instance.nextBtn.disabled = pageNumber >= instance.pdfDoc.numPages;
        }
    },

    bindEvents: function (instance) {
        const self = this;

        instance.prevBtn.addEventListener("click", () => {
            if (instance.currentPage > 1) {
                self.renderPage(instance, instance.currentPage - 1);
            }
        });

        instance.nextBtn.addEventListener("click", () => {
            if (instance.currentPage < instance.pdfDoc.numPages) {
                self.renderPage(instance, instance.currentPage + 1);
            }
        });

        // 键盘快捷键（只绑定一次）
        if (this.instances.size === 1) {
            document.addEventListener("keydown", (e) => {
                const activeInstance = Array.from(this.instances.values()).find((inst) => inst.pdfDoc);
                if (!activeInstance) return;

                switch (e.key) {
                    case "ArrowLeft":
                        if (activeInstance.currentPage > 1) this.renderPage(activeInstance, activeInstance.currentPage - 1);
                        break;
                    case "ArrowRight":
                        if (activeInstance.currentPage < activeInstance.pdfDoc.numPages) this.renderPage(activeInstance, activeInstance.currentPage + 1);
                        break;
                }
            });
        }
    },

    loadPDF: function (instance, url) {
        const self = this;
        const pdfjsLib = window["pdfjs-dist/build/pdf"];
        const loadingTask = pdfjsLib.getDocument(url);

        loadingTask.promise
            .then((pdf) => {
                instance.pdfDoc = pdf;
                if (instance.toolbar) {
                    instance.pageCountSpan.textContent = pdf.numPages;
                }

                // 隐藏加载提示
                instance.loading.style.display = "none";

                // 渲染第一页
                self.renderPage(instance, 1);
            })
            .catch((error) => {
                instance.loading.innerHTML =
                    '<div style="text-align: center; color: red;"><p>PDF加载失败</p><p style="font-size: 12px;">' + error.message + "</p></div>";
                console.error("PDF加载错误:", error);
            });
    },

    destroy: function (id) {
        const instance = this.instances.get(id);
        if (instance && instance.resizeObserver) {
            instance.resizeObserver.disconnect();
        }
        this.instances.delete(id);
    },
};
