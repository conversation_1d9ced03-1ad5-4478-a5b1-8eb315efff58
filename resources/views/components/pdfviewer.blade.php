@props([
    "url",
])

<div
    x-data="pdfViewer('{{ $url }}')"
    x-init="init()"
    x-on:destroy="destroy()"
    wire:ignore
    class="flex h-full w-full flex-col bg-gray-50 dark:bg-gray-900"
>
    <!-- PDF 控制栏 -->
    <div class="flex flex-shrink-0 items-center justify-between border-b border-gray-200 bg-white px-4 py-2 dark:border-gray-700 dark:bg-gray-800">
        <div class="flex items-center space-x-3">
            <button
                @click="previousPage()"
                :disabled="currentPage <= 1"
                class="rounded bg-blue-500 px-3 py-1 text-sm text-white transition-colors hover:bg-blue-600 disabled:cursor-not-allowed disabled:bg-gray-300"
            >
                上一页
            </button>

            <span class="text-sm whitespace-nowrap text-gray-600 dark:text-gray-300">
                第
                <span x-text="currentPage"></span>
                页 / 共
                <span x-text="totalPages"></span>
                页
            </span>

            <button
                @click="nextPage()"
                :disabled="currentPage >= totalPages"
                class="rounded bg-blue-500 px-3 py-1 text-sm text-white transition-colors hover:bg-blue-600 disabled:cursor-not-allowed disabled:bg-gray-300"
            >
                下一页
            </button>
        </div>

        <div class="flex items-center space-x-2">
            <button
                @click="zoomOut()"
                class="rounded bg-gray-200 px-2 py-1 text-sm text-gray-700 transition-colors hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
            >
                -
            </button>
            <span class="min-w-[3rem] text-center text-sm text-gray-600 dark:text-gray-300" x-text="Math.round(scale * 100) + '%'"></span>
            <button
                @click="zoomIn()"
                class="rounded bg-gray-200 px-2 py-1 text-sm text-gray-700 transition-colors hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
            >
                +
            </button>
        </div>
    </div>

    <!-- PDF 内容区域 -->
    <div class="relative flex-1 overflow-hidden">
        <div class="absolute inset-0 flex items-center justify-center" x-show="loading">
            <div class="text-gray-500 dark:text-gray-400">加载中...</div>
        </div>

        <div class="absolute inset-0 flex items-center justify-center" x-show="!loading && !error">
            <canvas x-ref="canvas" class="block" style="max-width: 100%; max-height: 100%"></canvas>
        </div>

        <div class="absolute inset-0 flex items-center justify-center" x-show="error">
            <div class="text-red-500 dark:text-red-400" x-text="error"></div>
        </div>
    </div>
</div>

<script>
    function pdfViewer(url) {
        return {
            url: url,
            pdfDoc: null,
            currentPage: 1,
            totalPages: 0,
            scale: 1.0,
            loading: true,
            error: null,
            renderTask: null,

            async init() {
                try {
                    // 检查 PDF.js 是否可用
                    if (typeof pdfjsLib === 'undefined') {
                        throw new Error('PDF.js 库未加载');
                    }

                    // 等待一小段时间确保DOM完全渲染
                    await new Promise((resolve) => setTimeout(resolve, 100));

                    // 加载 PDF 文档
                    const loadingTask = pdfjsLib.getDocument({
                        url: this.url,
                        cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@4.10.38/cmaps/',
                        cMapPacked: true,
                    });

                    this.pdfDoc = await loadingTask.promise;
                    this.totalPages = this.pdfDoc.numPages;

                    // 渲染第一页
                    await this.renderPage();
                    this.loading = false;

                    // 监听窗口大小变化
                    this.resizeHandler = () => {
                        if (!this.loading && this.pdfDoc) {
                            clearTimeout(this.resizeTimeout);
                            this.resizeTimeout = setTimeout(() => {
                                this.renderPage();
                            }, 150);
                        }
                    };
                    window.addEventListener('resize', this.resizeHandler);

                    // 监听键盘事件
                    this.keyHandler = (e) => {
                        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;

                        switch (e.key) {
                            case 'ArrowLeft':
                            case 'ArrowUp':
                                e.preventDefault();
                                this.previousPage();
                                break;
                            case 'ArrowRight':
                            case 'ArrowDown':
                                e.preventDefault();
                                this.nextPage();
                                break;
                            case '+':
                            case '=':
                                e.preventDefault();
                                this.zoomIn();
                                break;
                            case '-':
                                e.preventDefault();
                                this.zoomOut();
                                break;
                        }
                    };
                    document.addEventListener('keydown', this.keyHandler);
                } catch (err) {
                    console.error('PDF 加载失败:', err);
                    this.error = 'PDF 加载失败: ' + err.message;
                    this.loading = false;
                }
            },

            destroy() {
                // 清理事件监听器
                if (this.resizeHandler) {
                    window.removeEventListener('resize', this.resizeHandler);
                }
                if (this.keyHandler) {
                    document.removeEventListener('keydown', this.keyHandler);
                }
                // 清理定时器
                if (this.resizeTimeout) {
                    clearTimeout(this.resizeTimeout);
                }
                // 取消渲染任务
                if (this.renderTask) {
                    try {
                        this.renderTask.cancel();
                    } catch (e) {
                        // 忽略取消错误
                    }
                }
            },

            async renderPage() {
                if (!this.pdfDoc) return;

                try {
                    // 取消之前的渲染任务
                    if (this.renderTask) {
                        try {
                            this.renderTask.cancel();
                        } catch (e) {
                            // 忽略取消错误
                        }
                        this.renderTask = null;
                    }

                    const page = await this.pdfDoc.getPage(this.currentPage);
                    const canvas = this.$refs.canvas;

                    if (!canvas) {
                        console.error('Canvas element not found');
                        return;
                    }

                    const context = canvas.getContext('2d');
                    const container = canvas.parentElement;

                    // 获取容器尺寸
                    const containerWidth = container.clientWidth;
                    const containerHeight = container.clientHeight;

                    if (containerWidth === 0 || containerHeight === 0) {
                        // 容器尺寸为0，延迟渲染
                        setTimeout(() => this.renderPage(), 100);
                        return;
                    }

                    // 计算基础视口
                    const baseViewport = page.getViewport({ scale: 1.0 });

                    // 计算适应容器的缩放比例
                    const scaleX = containerWidth / baseViewport.width;
                    const scaleY = containerHeight / baseViewport.height;
                    const autoScale = Math.min(scaleX, scaleY) * 0.98; // 留一点边距

                    // 应用用户设置的缩放
                    const finalScale = autoScale * this.scale;

                    // 计算最终视口
                    const viewport = page.getViewport({ scale: finalScale });

                    // 清除画布
                    context.clearRect(0, 0, canvas.width, canvas.height);

                    // 设置画布尺寸
                    const outputScale = window.devicePixelRatio || 1;
                    canvas.width = Math.floor(viewport.width * outputScale);
                    canvas.height = Math.floor(viewport.height * outputScale);
                    canvas.style.width = Math.floor(viewport.width) + 'px';
                    canvas.style.height = Math.floor(viewport.height) + 'px';

                    const transform = outputScale !== 1 ? [outputScale, 0, 0, outputScale, 0, 0] : null;

                    // 渲染页面
                    const renderContext = {
                        canvasContext: context,
                        viewport: viewport,
                    };

                    // 只有在需要时才添加transform
                    if (transform) {
                        renderContext.transform = transform;
                    }

                    this.renderTask = page.render(renderContext);

                    try {
                        await this.renderTask.promise;
                        this.renderTask = null;
                    } catch (renderError) {
                        if (renderError.name !== 'RenderingCancelledException') {
                            throw renderError;
                        }
                    }
                } catch (err) {
                    console.error('页面渲染失败:', err);
                    this.error = '页面渲染失败: ' + err.message;
                    this.renderTask = null;
                }
            },

            async previousPage() {
                if (this.currentPage <= 1) return;
                this.currentPage--;
                await this.renderPage();
            },

            async nextPage() {
                if (this.currentPage >= this.totalPages) return;
                this.currentPage++;
                await this.renderPage();
            },

            async zoomIn() {
                this.scale = Math.min(this.scale * 1.2, 3.0);
                await this.renderPage();
            },

            async zoomOut() {
                this.scale = Math.max(this.scale / 1.2, 0.5);
                await this.renderPage();
            },
        };
    }
</script>
