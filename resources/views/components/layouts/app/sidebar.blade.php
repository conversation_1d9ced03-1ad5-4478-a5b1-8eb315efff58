<!DOCTYPE html>
<html lang="{{ str_replace("_", "-", app()->getLocale()) }}" class="dark">
    <head>
        @include("partials.head")
    </head>
    <body class="min-h-screen bg-white antialiased dark:bg-zinc-800">
        <flux:sidebar sticky stashable class="border-e border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900 [:where(&)]:w-72">
            <flux:sidebar.toggle class="lg:hidden" icon="x-mark" />

            <a href="{{ route("dashboard") }}" class="me-5 flex items-center space-x-2 rtl:space-x-reverse" wire:navigate>
                <x-app-logo />
            </a>

            <div class="space-y-8">
                <div>
                    <h2 class="text-base/7 font-semibold text-pretty text-gray-950 sm:text-sm/6 dark:text-white">第一单元：导入</h2>
                    <ul
                        class="mt-4 flex flex-col gap-4 border-l border-gray-950/10 text-base/7 text-gray-700 sm:mt-3 sm:gap-3 sm:text-sm/6 dark:border-white/10 dark:text-gray-400 [&>li]:-ml-px [&>li]:flex [&>li]:border-l [&>li]:border-transparent [&>li]:pl-4 [&>li]:hover:text-gray-950 [&>li]:hover:not-has-aria-[current=page]:border-gray-400 [&>li]:has-aria-[current=page]:border-gray-950 [&>li]:dark:hover:text-white [&>li]:dark:has-aria-[current=page]:border-white [&>li>a]:aria-[current=page]:font-medium [&>li>a]:aria-[current=page]:text-gray-950 [&>li>a]:dark:aria-[current=page]:text-white"
                    >
                        <li>
                            <a href="/landscape-of-choice">1.1 揭秘生活中的人工智能</a>
                        </li>
                        <li>
                            <a href="/paradox-of-agency">1.2 解锁智能密码</a>
                        </li>
                        <li>
                            <a href="/liberation-from-regret">1.3 AI发展史（选修）</a>
                        </li>
                    </ul>
                </div>
                <div>
                    <h2 class="text-base/7 font-semibold text-pretty text-gray-950 sm:text-sm/6 dark:text-white">第二单元：图像识别（AI）</h2>
                    <ul
                        class="mt-4 flex flex-col gap-4 border-l border-gray-950/10 text-base/7 text-gray-700 sm:mt-3 sm:gap-3 sm:text-sm/6 dark:border-white/10 dark:text-gray-400 [&>li]:-ml-px [&>li]:flex [&>li]:border-l [&>li]:border-transparent [&>li]:pl-4 [&>li]:hover:text-gray-950 [&>li]:hover:not-has-aria-[current=page]:border-gray-400 [&>li]:has-aria-[current=page]:border-gray-950 [&>li]:dark:hover:text-white [&>li]:dark:has-aria-[current=page]:border-white [&>li>a]:aria-[current=page]:font-medium [&>li>a]:aria-[current=page]:text-gray-950 [&>li>a]:dark:aria-[current=page]:text-white"
                    >
                        <li><a href="#">2.1 点名神器：用图像识别解决校园考勤难题</a></li>
                        <li><a href="#" aria-current="page">2.2 兴趣课匹配师：数据清洗帮你选课程</a></li>
                        <li><a href="#">2.3 设计校园植物角：分类数据让绿化更美好</a></li>
                        <li><a href="#">2.4 台风后的植物角：用标签快速找到植物归属</a></li>
                        <li><a href="#">2.5 当植物遗失标签后：找回遗失的植物标签</a></li>
                        <li><a href="#">2.6 图像变清晰：图像预处理的基础运算</a></li>
                        <li><a href="#">2.7 谁在动？用图像运算检测运动轨迹</a></li>
                        <li><a href="#">2.8 你画我猜：揭秘图像中的字符秘密</a></li>
                        <li><a href="#">2.9 手写字解析：汉明距离</a></li>
                        <li><a href="#">2.10 项目实践：认识K210</a></li>
                        <li><a href="#">2.11 校内设备更易管理：认识变量</a></li>
                        <li><a href="#">2.12 球类运动追踪器：AI帮你统计运动数据+</a></li>
                        <li><a href="#">2.13 学期回顾：图像识别从原理到实践</a></li>
                        <li><a href="#">2.14 拓展课：AI帮你统计运动数据-</a></li>
                    </ul>
                </div>
            </div>

            <flux:spacer />
        </flux:sidebar>

        <flux:header sticky class="justify-between border-b border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900">
            <flux:breadcrumbs>
                <flux:breadcrumbs.item href="#" separator="slash">初中新七年级（上）</flux:breadcrumbs.item>
                <flux:breadcrumbs.item separator="slash">第二单元：图像识别（AI）</flux:breadcrumbs.item>
                <flux:breadcrumbs.item separator="slash">2.2 兴趣课匹配师：数据清洗帮你选课程</flux:breadcrumbs.item>
            </flux:breadcrumbs>
            <flux:dropdown position="top" align="start">
                <flux:profile :name="auth()->user()->name" :initials="auth()->user()->initials()" icon:trailing="chevrons-up-down" />

                <flux:menu class="w-[220px]">
                    <flux:menu.radio.group>
                        <div class="p-0 text-sm font-normal">
                            <div class="flex items-center gap-2 px-1 py-1.5 text-start text-sm">
                                <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                                    <span
                                        class="flex h-full w-full items-center justify-center rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white"
                                    >
                                        {{ auth()->user()->initials() }}
                                    </span>
                                </span>

                                <div class="grid flex-1 text-start text-sm leading-tight">
                                    <span class="truncate font-semibold">{{ auth()->user()->name }}</span>
                                    <span class="truncate text-xs">{{ auth()->user()->email }}</span>
                                </div>
                            </div>
                        </div>
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    <flux:menu.radio.group>
                        <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate>{{ __("Settings") }}</flux:menu.item>
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    <form method="POST" action="{{ route("logout") }}" class="w-full">
                        @csrf
                        <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                            {{ __("Log Out") }}
                        </flux:menu.item>
                    </form>
                </flux:menu>
            </flux:dropdown>
        </flux:header>

        {{ $slot }}

        @fluxScripts
    </body>
</html>
