@props(["initialCode" => null])

@php
    $defaultCode = $initialCode ?? "# 在这里编写 Python 代码\nprint('Hello, Python!')\n\n# 尝试一些数学运算\nimport math\nresult = math.sqrt(16)\nprint(f'平方根 16 = {result}')\n\n# 创建一个简单的列表\nnumbers = [1, 2, 3, 4, 5]\nprint(f'数字列表: {numbers}')\nprint(f'列表总和: {sum(numbers)}')";
    // 将代码进行 base64 编码以避免引号和换行符问题
    $encodedCode = base64_encode($defaultCode);
@endphp

{{-- 加载 Ace Editor --}}
<script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.32.6/ace.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.32.6/mode-python.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.32.6/theme-dracula.js"></script>

{{-- 加载 Pyodide CDN --}}
<script src="https://cdn.jsdelivr.net/pyodide/v0.24.1/full/pyodide.js"></script>

<script>
    // 在 Alpine.js 初始化之前定义数据
    document.addEventListener('alpine:init', () => {
        Alpine.data('pythonRunner', (encodedCode) => {
            // 解码 base64 编码的初始代码
            const initialCode = atob(encodedCode);

            return {
                pyodide: null,
                pyodideReady: false,
                output: '',
                aceEditor: null,
                isRunning: false,
                initialCode: initialCode,
                currentCode: initialCode,

                // 初始化 Ace Editor
                async initAceEditor() {
                    try {
                        console.log('开始初始化 Ace Editor...');

                        // 等待 Ace Editor 加载完成
                        let attempts = 0;
                        while (typeof ace === 'undefined' && attempts < 50) {
                            await new Promise((resolve) => setTimeout(resolve, 100));
                            attempts++;
                        }

                        if (typeof ace === 'undefined') {
                            throw new Error('Ace Editor 未加载');
                        }

                        // 创建 Ace Editor 实例
                        this.aceEditor = ace.edit(this.$refs.aceContainer);

                        // 配置编辑器
                        this.aceEditor.setTheme('ace/theme/dracula');
                        this.aceEditor.session.setMode('ace/mode/python');
                        this.aceEditor.container.style.lineHeight = '2em';
                        this.aceEditor.setOptions({
                            fontSize: 14,
                            fontFamily: 'Monolisa, Monaco, "Courier New", monospace',
                            showPrintMargin: false,
                            highlightActiveLine: false,
                            tabSize: 4,
                            useSoftTabs: true,
                            wrap: true,
                            showLineNumbers: true,
                            showGutter: true,
                        });

                        // 设置初始值
                        this.aceEditor.setValue(this.initialCode, -1);

                        // 监听代码变化
                        this.aceEditor.on('change', () => {
                            this.currentCode = this.aceEditor.getValue();
                        });

                        // 添加快捷键
                        this.aceEditor.commands.addCommand({
                            name: 'runCode',
                            bindKey: { win: 'Ctrl-Enter', mac: 'Cmd-Enter' },
                            exec: () => {
                                this.runCode();
                            },
                        });

                        console.log('Ace Editor 初始化成功！');
                    } catch (error) {
                        console.error('Ace Editor 初始化失败:', error);
                        this.fallbackToTextarea();
                    }
                },

                // 回退到简单的 textarea
                fallbackToTextarea() {
                    const container = this.$refs.aceContainer;
                    container.innerHTML = `
                    <textarea
                        class="h-full w-full resize-none bg-[#282a36] border border-zinc-700 font-mono text-sm leading-5 text-white caret-white focus:outline-none focus:ring-2 focus:ring-blue-500 p-4"
                        x-model="currentCode"
                        @keydown.ctrl.enter="runCode()"
                        @keydown.cmd.enter="runCode()"
                        spellcheck="false"
                        placeholder="在这里编写 Python 代码..."
                    ></textarea>
                `;
                    this.currentCode = this.initialCode;
                },

                async initPyodide() {
                    try {
                        console.log('开始加载 Pyodide...');

                        // 等待 Pyodide 脚本加载完成
                        if (typeof loadPyodide === 'undefined') {
                            this.output = '正在加载 Python 环境，请稍候...';
                            // 等待 loadPyodide 函数可用
                            await new Promise((resolve) => {
                                const checkPyodide = () => {
                                    if (typeof loadPyodide !== 'undefined') {
                                        resolve();
                                    } else {
                                        setTimeout(checkPyodide, 100);
                                    }
                                };
                                checkPyodide();
                            });
                        }

                        // 加载 Pyodide
                        this.pyodide = await loadPyodide({
                            indexURL: 'https://cdn.jsdelivr.net/pyodide/v0.24.1/full/',
                            stdout: (text) => {
                                this.output += text;
                            },
                            stderr: (text) => {
                                this.output += 'Error: ' + text;
                            },
                        });

                        console.log('Pyodide 加载完成');
                        this.pyodideReady = true;
                        this.output = 'Python 环境已准备就绪！\n';
                    } catch (error) {
                        console.error('Pyodide 加载失败:', error);
                        this.output = 'Python 环境加载失败: ' + error.message;
                    }
                },

                // 运行代码
                async runCode() {
                    if (!this.pyodideReady) {
                        this.output = 'Python 环境尚未准备就绪，请稍候...';
                        return;
                    }

                    const code = this.currentCode.trim();
                    if (!code) {
                        this.output = '请输入要执行的 Python 代码';
                        return;
                    }

                    this.isRunning = true;

                    try {
                        // 清空之前的输出
                        this.output = '';

                        // 执行 Python 代码
                        const result = this.pyodide.runPython(code);

                        // 如果有返回值且不是 None，显示返回值
                        if (result !== undefined && result !== null && result.toString() !== 'None') {
                            this.output += '\n>>> ' + result.toString();
                        }

                        // 如果没有任何输出，显示执行完成信息
                        if (!this.output.trim()) {
                            this.output = '代码执行完成（无输出）';
                        }
                    } catch (error) {
                        console.error('Python 代码执行错误:', error);
                        this.output = 'Python 执行错误:\n' + error.message;
                    } finally {
                        this.isRunning = false;
                    }
                },

                // 清空输出
                clearOutput() {
                    this.output = '';
                },

                // 重置代码
                resetCode() {
                    if (this.aceEditor) {
                        this.aceEditor.setValue(this.initialCode, -1);
                    } else {
                        this.currentCode = this.initialCode;
                    }
                    this.clearOutput();
                },
            };
        });
    });
</script>

<div class="flex h-full flex-col" x-data="pythonRunner('{{ $encodedCode }}')" x-init="initPyodide()">
    {{-- 标题栏 --}}
    <div class="bg-[#131319] p-4">
        <div class="mb-2 flex items-center justify-between">
            <h3 class="font-semibold text-white">Python Playground</h3>
            <div class="flex items-center gap-2">
                <div x-show="!pyodideReady" class="flex items-center gap-1 text-xs text-yellow-400">
                    <svg class="h-3 w-3 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path
                            class="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                    </svg>
                    加载中
                </div>
                <div x-show="pyodideReady" class="flex items-center gap-1 text-xs text-green-400">
                    <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                        <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                        ></path>
                    </svg>
                    就绪
                </div>
            </div>
        </div>
        <p class="text-sm text-zinc-400">在下方编写 Python 代码并点击运行 (Ctrl+Enter)</p>
    </div>

    {{-- 代码编辑器 --}}
    <div class="flex flex-1 flex-col">
        <div class="w-full flex-1" x-ref="aceContainer" x-init="initAceEditor()"></div>

        {{-- 控制按钮 --}}
        <div class="border-b border-zinc-700 p-4">
            <div class="mb-3 flex gap-2">
                <button
                    @click="runCode()"
                    :disabled="isRunning || !pyodideReady"
                    class="flex-1 rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-700 disabled:cursor-not-allowed disabled:bg-gray-600"
                >
                    <span x-show="!isRunning">▶ 运行代码</span>
                    <span x-show="isRunning" class="flex items-center justify-center gap-2">
                        <svg class="h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path
                                class="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                        </svg>
                        运行中...
                    </span>
                </button>

                <button
                    @click="resetCode()"
                    class="rounded-md bg-transparent px-4 py-2 text-sm text-zinc-400 transition-colors hover:bg-zinc-700 hover:text-white"
                    title="重置为初始代码"
                >
                    🔄 重置
                </button>

                <button
                    @click="clearOutput()"
                    class="rounded-md bg-transparent px-4 py-2 text-sm text-zinc-400 transition-colors hover:bg-zinc-700 hover:text-white"
                >
                    🗑 清空
                </button>
            </div>
        </div>

        {{-- 输出结果 --}}
        <div class="flex-0">
            <label class="mb-2 block text-sm font-medium text-zinc-300">运行结果:</label>
            <div class="h-48 overflow-y-auto bg-[#292A36] p-3">
                <div x-show="!pyodideReady" class="text-sm text-zinc-400">
                    <div class="flex items-center gap-2">
                        <svg class="h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path
                                class="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                        </svg>
                        正在加载 Python 环境...
                    </div>
                </div>

                <div x-show="pyodideReady && !output" class="text-sm text-zinc-500 italic">点击"运行代码"查看输出结果</div>

                <pre x-show="output" class="font-mono text-sm whitespace-pre-wrap text-green-400" x-text="output"></pre>
            </div>
        </div>
    </div>
</div>
